{"name": "rudder-transformer", "version": "1.101.0", "description": "", "homepage": "https://github.com/rudderlabs/rudder-transformer#readme", "bugs": {"url": "https://github.com/rudderlabs/rudder-transformer/issues"}, "repository": {"type": "git", "url": "git+https://github.com/rudderlabs/rudder-transformer.git"}, "license": "ISC", "author": "", "main": "GATransform.js", "scripts": {"copy": "find ./src -name '*.yaml' -o -name '*.csv' | cpio -pdm ./dist", "clean": "rm -rf ./dist ./dist-test", "setup": "npm ci", "setup:swagger": "swagger-cli bundle swagger/api.yaml --outfile dist/swagger.json --type json", "format": "prettier --write .", "lint:fix": "eslint . --fix", "lint:fix:json": "eslint --ext .json --fix .", "lint": "npm run format && npm run lint:fix", "check:merge": "npm run verify || exit 1; codecov", "start": "cd dist && node ./src/index.js && cd ..", "build:start": "npm run build && npm run start", "build:ci": "tsc -p tsconfig.json", "build:swagger": "npm run build && npm run setup:swagger", "build:test": "tsc -p tsconfig.test.json", "build": "npm run build:ci && npm run copy", "clean:build": "npm run clean && npm run build", "build:clean": "npm run clean && npm run build", "verify": "eslint . || exit 1; npm run test:js || exit 1", "test:testRouter": "jest testRouter --detectO<PERSON><PERSON>andles --coverage --notify --watchAll=false", "test:benchmark": "node benchmark/index.js", "test": "NODE_OPTIONS='--no-node-snapshot' jest -c jest.config.js --detectOpenHandles", "test:ci": "npm run test -- --coverage --expand --maxWorkers=50%", "test:js": "NODE_OPTIONS='--no-node-snapshot' jest -c jest.default.config.js --detectOpenHandles", "test:js:silent": "export LOG_LEVEL=silent && npm run test:js -- --silent", "test:js:ci": "npm run test:js -- --coverage --expand --maxWorkers=50%", "test:ts": "NODE_OPTIONS='--no-node-snapshot' jest -c jest.config.typescript.js --detectOpenHandles", "test:ts:component:generateNwMocks": "npm run test:ts -- component --generate=true", "test:ts:component:rudder_test": "npm run test:ts -- component --destination=rudder_test", "test:ts:silent": "export LOG_LEVEL=silent && npm run test:ts -- --silent", "test:ts:ci": "npm run test:ts -- --coverage --expand --maxWorkers=50%", "test:ut:integration": "jest \"user_transformation.integration.test.js\"  --detectOpenHandles --notify", "test:ut:integration:silent": "npm run test:ut:integration -- --silent", "test:ut:integration:ci": "npm run test:ut:integration -- --expand --maxWorkers=50%", "pre-commit": "npm run test:ts:silent && npm run test:js:silent && npx lint-staged", "commit-msg": "commitlint --edit", "prepare": "node ./scripts/skipPrepareScript.js || husky install", "release": "npx standard-version", "release:github": "DEBUG=conventional-github-releaser npx conventional-github-releaser -p angular --config github-release.config.js", "clean:node": "modclean", "check:lint": "eslint . -f json -o reports/eslint.json || exit 0", "generate:testcases": "ts-node ./test/scripts/generateJson.ts --location testcases"}, "dependencies": {"@amplitude/ua-parser-js": "0.7.24", "@aws-sdk/client-personalize": "^3.616.0", "@aws-sdk/client-s3": "^3.474.0", "@aws-sdk/credential-providers": "^3.391.0", "@aws-sdk/lib-storage": "^3.637.0", "@bugsnag/js": "^7.20.2", "@datadog/pprof": "^5.5.1", "@koa/router": "^12.0.0", "@ndhoule/extend": "^2.0.0", "@rudderstack/integrations-lib": "^0.2.34", "@rudderstack/json-template-engine": "^0.19.5", "@rudderstack/pyroscope-nodejs": "^0.4.6-fork.1", "@rudderstack/workflow-engine": "^0.8.13", "@shopify/jest-koa-mocks": "^5.1.1", "ajv": "^8.17.1", "ajv-draft-04": "^1.0.0", "ajv-formats": "^2.1.1", "amazon-dsp-formatter": "^1.0.2", "axios": "^1.7.9", "btoa": "^1.2.1", "component-each": "^0.2.6", "crypto-js": "^4.2.0", "dotenv": "^16.0.3", "fast-json-stable-stringify": "^2.1.0", "fast-xml-parser": "^4.5.1", "flat": "^5.0.2", "form-data": "^4.0.0", "get-value": "^3.0.1", "handlebars": "^4.7.7", "http-graceful-shutdown": "^3.1.13", "https-proxy-agent": "^5.0.1", "ioredis": "^5.3.2", "is": "^3.3.0", "is-ip": "^3.1.0", "isolated-vm": "5.0.3", "js-sha1": "^0.6.0", "json-diff": "^1.0.3", "json-size": "^1.0.0", "jsontoxml": "^1.0.1", "koa": "^2.15.4", "koa-bodyparser": "^4.4.0", "koa2-swagger-ui": "^5.7.0", "libphonenumber-js": "^1.11.18", "lodash": "^4.17.21", "match-json": "^1.3.5", "md5": "^2.3.0", "modclean": "^3.0.0-beta.1", "moment": "^2.29.4", "moment-timezone": "^0.5.47", "node-cache": "^5.1.2", "node-fetch": "^2.6.12", "oauth-1.0a": "^2.2.6", "object-hash": "^3.0.0", "parse-static-imports": "^1.1.0", "prom-client": "^14.2.0", "qs": "^6.11.1", "rs-jsonpath": "^1.1.2", "set-value": "^4.1.0", "sha256": "^0.2.0", "sqlstring": "^2.3.3", "stacktrace-parser": "^0.1.10", "truncate-utf8-bytes": "^1.0.2", "ua-parser-js": "^1.0.37", "unset-value": "^2.0.1", "uuid": "^9.0.1", "valid-url": "^1.0.9", "validator": "^13.12.0", "zod": "^3.22.4"}, "devDependencies": {"@commitlint/config-conventional": "^19.8.0", "@digitalroute/cz-conventional-changelog-for-jira": "^8.0.1", "@types/fast-json-stable-stringify": "^2.1.0", "@types/jest": "^29.5.1", "@types/jsonpath": "^0.2.4", "@types/koa": "^2.15.0", "@types/koa-bodyparser": "^4.3.10", "@types/lodash": "^4.14.197", "@types/node": "^20.2.5", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^5.61.0", "@typescript-eslint/parser": "^5.59.2", "allure-jest": "^3.0.7", "axios-mock-adapter": "^1.22.0", "benchmark-suite": "^0.1.8", "commander": "^10.0.1", "commitizen": "^4.3.0", "commitlint": "^19.8.0", "conventional-github-releaser": "^3.1.5", "eslint": "^8.40.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-json": "^3.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-sonarjs": "^0.19.0", "eslint-plugin-unicorn": "^46.0.1", "glob": "^10.3.3", "http-terminator": "^3.2.0", "husky": "^9.1.6", "jest": "^29.5.0", "jest-diff": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-sonar": "^0.2.16", "jest-when": "^3.5.2", "lint-staged": "^15.5.0", "madge": "^6.1.0", "mocked-env": "^1.3.5", "node-notifier": "^10.0.1", "prettier": "^3.2.4", "semver": "^7.5.3", "standard-version": "^9.5.0", "supertest": "^6.3.3", "swagger-cli": "^4.0.4", "ts-jest": "^29.2.5", "typescript": "^5.0.4"}, "overrides": {"trim-newlines": "3.0.1", "dot-prop": "4.2.1", "semver-regex": "3.1.4", "cacheable-request@2.1.4": {"http-cache-semantics": "4.1.1"}}, "lint-staged": {"*.{js,ts}": "eslint --cache --fix", "*.{json,js,ts,md}": "prettier --write"}, "config": {"commitizen": {"path": "./node_modules/@digitalroute/cz-conventional-changelog-for-jira", "jiraMode": false, "jiraOptional": true, "skipScope": true, "defaultType": "feat"}}}