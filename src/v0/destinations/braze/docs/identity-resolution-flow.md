# Braze Identity Resolution Flow - Detailed Documentation

## Overview

The Braze Identity Resolution Flow is a **non-batched, direct API call** mechanism that merges anonymous users (identified by `anonymousId` or custom alias) with identified users (identified by `userId` or `brazeExternalId`). This flow uses <PERSON>raze's `/users/identify` endpoint to perform identity resolution and is triggered during `IDENTIFY` event processing.

## Business Use Case

### Problem Statement
In user journey tracking, users often start as anonymous visitors and later become identified users through login, registration, or other identification events. Without proper identity resolution:

1. **Data Fragmentation**: User data gets split between anonymous and identified profiles
2. **Incomplete User Journey**: Analytics lose track of pre-identification user behavior
3. **Duplicate Profiles**: Multiple profiles exist for the same physical user
4. **Attribution Issues**: Marketing attribution becomes inaccurate

### Solution
The identity resolution flow solves this by:
- **Merging Profiles**: Combines anonymous user data with identified user data
- **Preserving History**: Maintains complete user journey from anonymous to identified state
- **Unified Analytics**: Provides single view of user across their entire lifecycle
- **Data Consistency**: Ensures all user touchpoints are attributed to one profile

## Trigger Conditions

The identity resolution flow is triggered when **ALL** of the following conditions are met during an `IDENTIFY` event:

### Primary Condition
```javascript
const integrationsObj = getIntegrationsObj(message, 'BRAZE');
const isAliasPresent = isDefinedAndNotNull(integrationsObj?.alias);
const brazeExternalID = getDestinationExternalID(message, 'brazeExternalId') || message.userId;

if ((message.anonymousId || isAliasPresent) && brazeExternalID) {
  await processIdentify({ message, destination });
}
```

### Detailed Conditions

1. **Anonymous Identifier Present**: Either of the following must exist:
   - `message.anonymousId` is present
   - Custom alias is provided via `integrations.BRAZE.alias` object

2. **External Identifier Present**: Either of the following must exist:
   - `message.userId` is present
   - `brazeExternalId` is provided via context external IDs

### Condition Examples

#### ✅ Triggers Identity Resolution
```javascript
// Case 1: anonymousId + userId
{
  "type": "identify",
  "anonymousId": "anon_123",
  "userId": "user_456",
  "traits": { "email": "<EMAIL>" }
}

// Case 2: Custom alias + userId
{
  "type": "identify",
  "userId": "user_456",
  "integrations": {
    "BRAZE": {
      "alias": {
        "alias_name": "custom_anon_123",
        "alias_label": "customer_id"
      }
    }
  }
}

// Case 3: anonymousId + brazeExternalId
{
  "type": "identify",
  "anonymousId": "anon_123",
  "context": {
    "externalId": [
      {
        "type": "brazeExternalId",
        "id": "braze_user_456"
      }
    ]
  }
}
```

#### ❌ Does NOT Trigger Identity Resolution
```javascript
// Case 1: Only userId (no anonymous identifier)
{
  "type": "identify",
  "userId": "user_456",
  "traits": { "email": "<EMAIL>" }
}

// Case 2: Only anonymousId (no external identifier)
{
  "type": "identify",
  "anonymousId": "anon_123",
  "traits": { "email": "<EMAIL>" }
}

// Case 3: Neither anonymous nor external identifier
{
  "type": "identify",
  "traits": { "email": "<EMAIL>" }
}
```

## API Implementation Details

### Endpoint
- **URL**: `{dataCenter}/users/identify`
- **Method**: `POST`
- **Authentication**: Bearer token via `Authorization` header

### Request Headers
```javascript
{
  "Content-Type": "application/json",
  "Accept": "application/json",
  "Authorization": "Bearer {restApiKey}"
}
```

### Request Payload Structure

The payload follows Braze's `/users/identify` API specification:

```javascript
{
  "aliases_to_identify": [
    {
      "user_alias": {
        "alias_name": "string",
        "alias_label": "string"
      },
      "external_id": "string"
    }
  ],
  "merge_behavior": "merge"
}
```

### Payload Construction Logic

#### 1. Alias Object Construction (`setAliasObject`)
```javascript
// Priority 1: Custom alias from integrations object
if (integrationsObj?.alias?.alias_name && integrationsObj?.alias?.alias_label) {
  user_alias = {
    alias_name: integrationsObj.alias.alias_name,
    alias_label: integrationsObj.alias.alias_label
  };
}
// Priority 2: Default alias using anonymousId
else if (message.anonymousId) {
  user_alias = {
    alias_name: message.anonymousId,
    alias_label: "rudder_id"
  };
}
```

#### 2. External ID Construction (`setExternalId`)
```javascript
// Priority 1: brazeExternalId from context
const externalId = getDestinationExternalID(message, 'brazeExternalId') || message.userId;
if (externalId) {
  external_id = externalId;
}
```

### Example API Calls

#### Example 1: Standard anonymousId + userId
**Input Message:**
```javascript
{
  "type": "identify",
  "anonymousId": "anon_12345",
  "userId": "user_67890",
  "traits": { "email": "<EMAIL>" }
}
```

**API Payload:**
```javascript
{
  "aliases_to_identify": [
    {
      "user_alias": {
        "alias_name": "anon_12345",
        "alias_label": "rudder_id"
      },
      "external_id": "user_67890"
    }
  ],
  "merge_behavior": "merge"
}
```

#### Example 2: Custom alias + brazeExternalId
**Input Message:**
```javascript
{
  "type": "identify",
  "userId": "user_67890",
  "context": {
    "externalId": [
      {
        "type": "brazeExternalId",
        "id": "braze_ext_123"
      }
    ]
  },
  "integrations": {
    "BRAZE": {
      "alias": {
        "alias_name": "temp_user_456",
        "alias_label": "session_id"
      }
    }
  }
}
```

**API Payload:**
```javascript
{
  "aliases_to_identify": [
    {
      "user_alias": {
        "alias_name": "temp_user_456",
        "alias_label": "session_id"
      },
      "external_id": "braze_ext_123"
    }
  ],
  "merge_behavior": "merge"
}
```

## Response Handling

### Success Response
```javascript
{
  "aliases_processed": 1,
  "message": "success"
}
```

### Error Response
```javascript
{
  "aliases_processed": 0,
  "message": "success",
  "errors": [
    {
      "type": "'external_id' is required",
      "input_array": "user_identifiers",
      "index": 0
    }
  ]
}
```

### Error Handling Logic
1. **HTTP Status Check**: Validates response status using `isHttpStatusSuccess()`
2. **Network Error**: Throws `NetworkError` for non-success HTTP status codes
3. **Alias Failure Tracking**: Monitors `aliases_processed` count for statistics
4. **Stats Collection**: Increments failure counters for monitoring

## Flow Sequence

1. **Condition Evaluation**: Check if identity resolution conditions are met
2. **Payload Construction**: Build the identify payload with alias and external_id
3. **API Call**: Make direct HTTP POST to `/users/identify` endpoint
4. **Response Validation**: Check for HTTP and application-level errors
5. **Statistics Collection**: Track success/failure metrics
6. **Continue Processing**: Proceed with normal identify event processing

## Key Characteristics

### Non-Batched Nature
- **Direct API Call**: Bypasses all batching mechanisms
- **Immediate Execution**: Runs synchronously during event processing
- **Blocking Operation**: Must complete before proceeding with attribute updates

### Timing in Processing Pipeline
- **Pre-Attribute Processing**: Executes before user attributes are sent
- **Identity First**: Ensures proper user identity before data updates
- **Sequential Processing**: Runs in order within the transformation pipeline

## Error Scenarios and Edge Cases

### Common Error Scenarios
1. **Missing External ID**: When external_id is not properly set
2. **Invalid Alias**: When alias_name or alias_label are malformed
3. **API Rate Limits**: When Braze API rate limits are exceeded
4. **Authentication Failures**: When API key is invalid or expired

### Edge Cases
1. **Duplicate Aliases**: When user already has an alias with the same label
2. **Existing External ID**: When external_id already exists for different user
3. **Network Timeouts**: When API calls exceed timeout limits
4. **Partial Failures**: When some aliases process successfully, others fail

## Monitoring and Statistics

### Metrics Collected
- `braze_alias_failure_count`: Tracks failed identity resolution attempts
- Response time metrics for identity resolution calls
- Success/failure rates for monitoring

### Debugging Information
- Full request/response logging for troubleshooting
- Error details for failed identity resolution attempts
- Statistics on alias processing success rates

## Related Documentation
- [Braze Users Identify API](https://www.braze.com/docs/api/endpoints/user_data/post_user_identify/)
- [Braze User Aliasing](https://www.braze.com/docs/user_guide/data_and_analytics/user_data_collection/user_profile_lifecycle/#user-aliases)
- [RudderStack Braze Destination](../README.md)
